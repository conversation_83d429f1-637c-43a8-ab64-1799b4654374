# Programming Assistant Agent Framework

A comprehensive, high-performance programming assistant agent framework with plugin-based functionality, built on top of AutoGen.

## 🚀 Features

- **Modular Architecture**: Plugin-based system for extensible functionality
- **High Performance**: Concurrent processing with sub-500ms response times
- **Context Management**: Intelligent codebase understanding and session retention
- **Multi-Language Support**: Works with various programming languages and workflows
- **Tool Integration**: Standardized interfaces for IDE and external tool integration
- **Async Task Processing**: Priority-based task queuing with error handling
- **Event-Driven Communication**: Loose coupling between components via message broker
- **Resource Management**: Intelligent memory and CPU usage optimization

## 🏗️ Architecture Overview

The framework consists of five core components:

1. **Agent Core Engine** (`agent_framework/core/`) - Central orchestration system
2. **Plugin System** (`agent_framework/plugins/`) - Dynamic plugin loading and management
3. **Communication Layer** (`agent_framework/communication/`) - Event-driven messaging
4. **Context Management** (`agent_framework/context/`) - Intelligent context retention
5. **Task Execution Framework** (`agent_framework/execution/`) - Async task processing

## 📦 Installation

```bash
# Clone the repository
git clone <repository-url>
cd agent-test

# Install dependencies
uv sync

# Or with pip
pip install -r requirements.txt
```

## 🚀 Quick Start

### Basic Usage

```python
import asyncio
from agent_framework import AgentOrchestrator, FrameworkConfig, Task, TaskPriority

async def main():
    # Create configuration
    config = FrameworkConfig()
    config.model.api_key = "your-api-key"

    # Initialize orchestrator
    orchestrator = AgentOrchestrator(config)
    await orchestrator.initialize()

    # Execute a task
    task = Task(
        name="Code Analysis",
        task_type="code_analysis",
        priority=TaskPriority.HIGH,
        parameters={"code": "def hello(): print('world')"}
    )

    result = await orchestrator.execute_task(task)
    print(f"Result: {result.result}")

    # Use agent for assistance
    response = await orchestrator.run_agent_task(
        "Analyze this code for improvements: def sum(a,b): return a+b"
    )
    print(response)

    await orchestrator.shutdown()

asyncio.run(main())
```

### Run Example

```bash
# Run the comprehensive example
python examples/basic_usage.py
```

## 🔌 Plugin System

The framework supports dynamic plugin loading with a standardized interface:

### Creating a Plugin

```python
from agent_framework.core.types import PluginInterface, PluginRequest, PluginResponse

class MyPlugin(PluginInterface):
    PLUGIN_NAME = "my_plugin"
    PLUGIN_VERSION = "1.0.0"

    async def initialize(self, config):
        self._config = config

    async def execute(self, request: PluginRequest) -> PluginResponse:
        # Plugin logic here
        return PluginResponse(success=True, result="Done")

    async def get_capabilities(self):
        return [...]  # List of capabilities

    async def cleanup(self):
        pass
```

### Available Plugins

- **Code Analysis Plugin**: Complexity analysis, quality metrics, pattern detection
- **Refactoring Plugin**: Code improvement suggestions (planned)
- **Documentation Plugin**: Auto-documentation generation (planned)

## 📊 Performance Specifications

- **Concurrent Tasks**: Up to 10 concurrent task executions (configurable)
- **Response Time**: Sub-500ms for common operations
- **Memory Optimization**: Efficient handling of large codebases (>100k lines)
- **Caching**: Multi-level caching for frequently accessed patterns
- **Resource Monitoring**: Real-time CPU, memory, and task metrics

## 🛠️ Configuration

### Environment Variables

```bash
export AGENT_API_KEY="your-openrouter-api-key"
export AGENT_MODEL="qwen/qwen3-coder:free"
export AGENT_DEBUG="true"
```

### Configuration File

```python
config = FrameworkConfig()
config.execution.max_concurrent_tasks = 20
config.plugins.auto_load_plugins = True
config.cache.enabled = True
config.cache.cache_type = "memory"  # or "disk", "redis"
```

## 📁 Project Structure

```
agent-test/
├── agent_framework/           # Core framework
│   ├── core/                 # Core engine components
│   ├── plugins/              # Plugin system
│   ├── communication/        # Message broker
│   ├── context/              # Context management
│   └── execution/            # Task execution
├── plugins/                  # Plugin implementations
├── examples/                 # Usage examples
├── tests/                    # Test suite
└── docs/                     # Documentation
```

## 🧪 Testing

```bash
# Run tests (when implemented)
python -m pytest tests/

# Run with coverage
python -m pytest tests/ --cov=agent_framework
```

## 📚 Documentation

- [Architecture Guide](docs/architecture.md) - Detailed system design
- [Plugin Development](docs/plugins.md) - How to create plugins
- [API Reference](docs/api.md) - Complete API documentation
- [Configuration Guide](docs/configuration.md) - Configuration options
- [Performance Tuning](docs/performance.md) - Optimization guidelines

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details.

## 🙏 Acknowledgments

Built on top of the excellent [AutoGen](https://github.com/microsoft/autogen) framework by Microsoft.