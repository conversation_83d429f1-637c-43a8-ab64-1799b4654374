"""
Core orchestrator for the agent framework.

The AgentOrchestrator is the central coordination system that manages
agent lifecycle, task execution, and coordination between components.
"""

import asyncio
import logging
import time
from typing import Any, Dict, List, Optional
from uuid import UUID

try:
    import psutil
except ImportError:
    psutil = None

from autogen_agentchat.agents import AssistantAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient

from .config import FrameworkConfig
from .types import (
    Task, TaskResult, TaskStatus, TaskPriority,
    PluginInterface, PluginRequest, PluginResponse,
    ContextQuery, Context, AgentEvent, ResourceMetrics
)
from ..plugins.manager import PluginManager
from ..context.manager import ContextManager
from ..execution.executor import TaskExecutor
from ..communication.broker import MessageBroker


class AgentOrchestrator:
    """
    Central orchestration system for the agent framework.

    Manages agent lifecycle, task execution, plugin coordination,
    and communication between all framework components.
    """

    def __init__(self, config: FrameworkConfig):
        """Initialize the orchestrator with configuration."""
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Core components
        self._model_client: Optional[OpenAIChatCompletionClient] = None
        self._agent: Optional[AssistantAgent] = None
        self._plugin_manager: Optional[PluginManager] = None
        self._context_manager: Optional[ContextManager] = None
        self._task_executor: Optional[TaskExecutor] = None
        self._message_broker: Optional[MessageBroker] = None

        # State management
        self._is_initialized = False
        self._is_running = False
        self._shutdown_event = asyncio.Event()

        # Metrics and monitoring
        self._start_time: Optional[float] = None
        self._task_count = 0
        self._error_count = 0

    async def initialize(self) -> None:
        """Initialize all framework components."""
        if self._is_initialized:
            self.logger.warning("Orchestrator already initialized")
            return

        try:
            self.logger.info("Initializing agent framework...")
            self._start_time = time.time()

            # Validate configuration
            config_errors = self.config.validate_config()
            if config_errors:
                raise ValueError(f"Configuration errors: {', '.join(config_errors)}")

            # Initialize model client
            await self._initialize_model_client()

            # Initialize core components
            await self._initialize_components()

            # Initialize agent
            await self._initialize_agent()

            # Load plugins
            await self._load_plugins()

            self._is_initialized = True
            self._is_running = True
            self.logger.info("Agent framework initialized successfully")

            # Emit initialization event
            await self._emit_event("framework.initialized", {
                "config": self.config.model_dump(),
                "components": self._get_component_status()
            })

        except Exception as e:
            self.logger.error(f"Failed to initialize framework: {e}")
            await self.shutdown()
            raise

    async def _initialize_model_client(self) -> None:
        """Initialize the AI model client."""
        self.logger.info("Initializing model client...")

        self._model_client = OpenAIChatCompletionClient(
            model=self.config.model.model,
            api_key=self.config.model.api_key,
            base_url=self.config.model.base_url,
            model_info=self.config.model.model_info,
        )

        self.logger.info(f"Model client initialized: {self.config.model.model}")

    async def _initialize_components(self) -> None:
        """Initialize all framework components."""
        self.logger.info("Initializing framework components...")

        # Initialize message broker first (other components depend on it)
        from ..communication.broker import MessageBroker
        self._message_broker = MessageBroker(self.config)
        await self._message_broker.initialize()

        # Initialize context manager
        from ..context.manager import ContextManager
        self._context_manager = ContextManager(self.config, self._message_broker)
        await self._context_manager.initialize()

        # Initialize task executor
        from ..execution.executor import TaskExecutor
        self._task_executor = TaskExecutor(self.config, self._message_broker)
        await self._task_executor.initialize()

        # Initialize plugin manager
        from ..plugins.manager import PluginManager
        self._plugin_manager = PluginManager(self.config, self._message_broker)
        await self._plugin_manager.initialize()

        self.logger.info("Framework components initialized")

    async def _initialize_agent(self) -> None:
        """Initialize the main assistant agent."""
        if not self._model_client:
            raise RuntimeError("Model client not initialized")

        self.logger.info("Initializing assistant agent...")

        # Get available tools from plugins
        tools = []
        if self._plugin_manager:
            tools = await self._plugin_manager.get_all_tools()

        # Create assistant agent
        self._agent = AssistantAgent(
            name="programming_assistant",
            model_client=self._model_client,
            tools=tools,
            system_message=self._get_system_message(),
            reflect_on_tool_use=True,
            model_client_stream=True,
        )

        self.logger.info("Assistant agent initialized")

    def _get_system_message(self) -> str:
        """Get the system message for the assistant agent."""
        return """You are a comprehensive programming assistant agent with access to various tools and plugins.

Your capabilities include:
- Code analysis and understanding
- Refactoring and optimization suggestions
- Test generation and validation
- Documentation generation
- Integration with development tools

Always provide helpful, accurate, and actionable assistance to developers.
Use the available tools and plugins to enhance your responses when appropriate.
"""

    async def _load_plugins(self) -> None:
        """Load and initialize plugins."""
        if not self._plugin_manager:
            self.logger.warning("Plugin manager not initialized, skipping plugin loading")
            return

        if self.config.plugins.auto_load_plugins:
            self.logger.info("Auto-loading plugins...")
            await self._plugin_manager.load_all_plugins()

        self.logger.info(f"Loaded {len(self._plugin_manager.get_loaded_plugins())} plugins")

    async def execute_task(self, task: Task) -> TaskResult:
        """Execute a task using the framework."""
        if not self._is_initialized:
            raise RuntimeError("Framework not initialized")

        if not self._task_executor:
            raise RuntimeError("Task executor not available")

        self.logger.info(f"Executing task: {task.name} ({task.id})")
        self._task_count += 1

        try:
            # Emit task started event
            await self._emit_event("task.started", {
                "task_id": str(task.id),
                "task_name": task.name,
                "task_type": task.task_type
            })

            # Execute the task
            result = await self._task_executor.execute_task(task)

            # Emit task completed event
            await self._emit_event("task.completed", {
                "task_id": str(task.id),
                "status": result.status.value,
                "execution_time": result.execution_time
            })

            return result

        except Exception as e:
            self._error_count += 1
            self.logger.error(f"Task execution failed: {e}")

            # Emit task failed event
            await self._emit_event("task.failed", {
                "task_id": str(task.id),
                "error": str(e)
            })

            return TaskResult(
                task_id=task.id,
                status=TaskStatus.FAILED,
                error=str(e)
            )

    async def load_plugin(self, plugin_name: str) -> PluginInterface:
        """Load a specific plugin by name."""
        if not self._plugin_manager:
            raise RuntimeError("Plugin manager not available")

        return await self._plugin_manager.load_plugin(plugin_name)

    async def get_context(self, query: ContextQuery) -> Context:
        """Retrieve context information."""
        if not self._context_manager:
            raise RuntimeError("Context manager not available")

        return await self._context_manager.get_context(query)

    async def run_agent_task(self, task_description: str) -> str:
        """Run a task using the assistant agent directly."""
        if not self._agent:
            raise RuntimeError("Agent not initialized")

        self.logger.info(f"Running agent task: {task_description}")

        try:
            result = await self._agent.run(task=task_description)

            # Extract the final response
            if result.messages:
                return result.messages[-1].content
            else:
                return "No response generated"

        except Exception as e:
            self.logger.error(f"Agent task failed: {e}")
            raise

    async def get_metrics(self) -> ResourceMetrics:
        """Get current system metrics."""
        import psutil

        # Get system metrics
        cpu_usage = psutil.cpu_percent()
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')

        # Get framework-specific metrics
        active_tasks = 0
        plugin_count = 0

        if self._task_executor:
            active_tasks = await self._task_executor.get_active_task_count()

        if self._plugin_manager:
            plugins = self._plugin_manager.get_loaded_plugins()
            plugin_count = len(plugins)

        return ResourceMetrics(
            cpu_usage=cpu_usage,
            memory_usage=memory.percent,
            disk_usage=disk.percent,
            network_usage=0.0,  # Would need network monitoring
            active_tasks=active_tasks,
            plugin_count=plugin_count
        )

    async def shutdown(self) -> None:
        """Shutdown the framework and cleanup resources."""
        if not self._is_initialized:
            return

        self.logger.info("Shutting down agent framework...")
        self._shutdown_event.set()

        try:
            # Shutdown components in reverse order
            if self._plugin_manager:
                await self._plugin_manager.shutdown()

            if self._task_executor:
                await self._task_executor.shutdown()

            if self._context_manager:
                await self._context_manager.shutdown()

            if self._message_broker:
                await self._message_broker.shutdown()

            if self._model_client:
                await self._model_client.close()

            # Emit shutdown event
            await self._emit_event("framework.shutdown", {
                "uptime": time.time() - (self._start_time or 0),
                "tasks_processed": self._task_count,
                "errors": self._error_count
            })

        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")

        self._is_initialized = False
        self._is_running = False
        self.logger.info("Agent framework shutdown complete")

    async def _emit_event(self, event_type: str, data: Dict[str, Any]) -> None:
        """Emit an event through the message broker."""
        if self._message_broker:
            event = AgentEvent(
                event_type=event_type,
                source="orchestrator",
                data=data
            )
            await self._message_broker.publish_event(event)

    def _get_component_status(self) -> Dict[str, bool]:
        """Get the status of all components."""
        return {
            "model_client": self._model_client is not None,
            "agent": self._agent is not None,
            "plugin_manager": self._plugin_manager is not None,
            "context_manager": self._context_manager is not None,
            "task_executor": self._task_executor is not None,
            "message_broker": self._message_broker is not None,
        }

    @property
    def is_initialized(self) -> bool:
        """Check if the framework is initialized."""
        return self._is_initialized

    @property
    def is_running(self) -> bool:
        """Check if the framework is running."""
        return self._is_running